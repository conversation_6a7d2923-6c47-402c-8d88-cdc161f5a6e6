import api from './api';

export const WORKFLOW_STEPS = {
  CREATE_CAMPAIGN: 1,
  UPLOAD_EMPLOYEES: 2,
  DEFINE_CRITERIA: 3,
  GENERATE_PAIRS: 4,
  CONFIRM_SEND: 5
};

// Cache for workflow status data
const workflowCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Batch request queue
let batchQueue = [];
let batchTimeout = null;
const BATCH_DELAY = 100; // 100ms delay to collect requests
const BATCH_SIZE = 20; // Maximum campaigns per batch

export const workflowService = {
  // OPTIMIZED: Get multiple campaign workflow statuses in a single request
  getBulkCampaignWorkflowStatus: async (campaignIds) => {
    try {
      const response = await api.post('/campaigns/bulk-workflow-status/', {
        campaign_ids: campaignIds
      });

      // Cache the results
      const now = Date.now();
      const workflowStates = response.data; // Backend returns array directly

      if (Array.isArray(workflowStates)) {
        workflowStates.forEach(workflowState => {
          const cacheKey = `workflow_${workflowState.campaign_id}`;
          workflowCache.set(cacheKey, {
            data: workflowState,
            timestamp: now
          });
        });
      }

      return workflowStates;
    } catch (error) {
      console.error('Error fetching bulk workflow status:', error);
      throw error.response?.data || error.message;
    }
  },

  // OPTIMIZED: Get campaign workflow status with intelligent batching
  getCampaignWorkflowStatus: async (campaignId) => {
    const cacheKey = `workflow_${campaignId}`;
    const now = Date.now();

    // Check cache first
    const cached = workflowCache.get(cacheKey);
    if (cached && (now - cached.timestamp) < CACHE_DURATION) {
      return cached.data;
    }

    // Return a promise that will be resolved when the batch request completes
    return new Promise((resolve, reject) => {
      // Add to batch queue
      batchQueue.push({ campaignId, resolve, reject });

      // Clear existing timeout
      if (batchTimeout) {
        clearTimeout(batchTimeout);
      }

      // Set new timeout to process batch
      batchTimeout = setTimeout(async () => {
        const currentBatch = [...batchQueue];
        batchQueue = [];
        batchTimeout = null;

        if (currentBatch.length === 0) return;

        try {
          // Extract unique campaign IDs
          const campaignIds = [...new Set(currentBatch.map(item => item.campaignId))];

          // Split into chunks if too many campaigns
          const chunks = [];
          for (let i = 0; i < campaignIds.length; i += BATCH_SIZE) {
            chunks.push(campaignIds.slice(i, i + BATCH_SIZE));
          }

          // Process all chunks
          const allWorkflowStates = [];
          for (const chunk of chunks) {
            const workflowStates = await workflowService.getBulkCampaignWorkflowStatus(chunk);
            allWorkflowStates.push(...workflowStates);
          }

          // Create a map for quick lookup
          const workflowMap = new Map();
          allWorkflowStates.forEach(ws => {
            workflowMap.set(ws.campaign_id, ws);
          });

          // Resolve all promises in the batch
          currentBatch.forEach(({ campaignId, resolve, reject }) => {
            const workflowState = workflowMap.get(campaignId);
            if (workflowState) {
              resolve(workflowState);
            } else {
              // Fallback to individual request if not found in batch
              workflowService.getCampaignWorkflowStatusDirect(campaignId)
                .then(resolve)
                .catch(reject);
            }
          });

        } catch (error) {
          // If batch fails, try individual requests
          console.warn('Batch request failed, falling back to individual requests:', error);
          currentBatch.forEach(({ campaignId, resolve, reject }) => {
            workflowService.getCampaignWorkflowStatusDirect(campaignId)
              .then(resolve)
              .catch(reject);
          });
        }
      }, BATCH_DELAY);
    });
  },

  // Direct individual request (fallback)
  getCampaignWorkflowStatusDirect: async (campaignId) => {
    try {
      const response = await api.get(`/campaigns/${campaignId}/workflow-status/`);

      // Cache the result
      const cacheKey = `workflow_${campaignId}`;
      workflowCache.set(cacheKey, {
        data: response.data,
        timestamp: Date.now()
      });

      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Clear cache (useful for testing or when data changes)
  clearCache: () => {
    workflowCache.clear();
  },

  // Get cached workflow status (synchronous)
  getCachedWorkflowStatus: (campaignId) => {
    const cacheKey = `workflow_${campaignId}`;
    const cached = workflowCache.get(cacheKey);
    const now = Date.now();

    if (cached && (now - cached.timestamp) < CACHE_DURATION) {
      return cached.data;
    }
    return null;
  },

  // Update workflow step completion
  updateWorkflowStep: async (campaignId, step, completed = true, data = {}) => {
    try {
      const response = await api.post(`/campaigns/${campaignId}/workflow-step/`, {
        step,
        completed,
        data
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Validate workflow step access
  validateWorkflowStep: async (campaignId, step) => {
    try {
      const response = await api.get(`/campaigns/${campaignId}/workflow-validate/${step}/`);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Reset workflow from a specific step
  resetWorkflowFromStep: async (campaignId, fromStep) => {
    try {
      const response = await api.post(`/campaigns/${campaignId}/workflow-reset/`, {
        from_step: fromStep
      });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get step requirements and dependencies
  getStepRequirements: (step) => {
    const requirements = {
      [WORKFLOW_STEPS.CREATE_CAMPAIGN]: {
        dependencies: [],
        description: 'Create a new campaign with basic information'
      },
      [WORKFLOW_STEPS.UPLOAD_EMPLOYEES]: {
        dependencies: [WORKFLOW_STEPS.CREATE_CAMPAIGN],
        description: 'Import employees via Excel file'
      },
      [WORKFLOW_STEPS.DEFINE_CRITERIA]: {
        dependencies: [WORKFLOW_STEPS.CREATE_CAMPAIGN, WORKFLOW_STEPS.UPLOAD_EMPLOYEES],
        description: 'Define matching criteria (optional)'
      },
      [WORKFLOW_STEPS.GENERATE_PAIRS]: {
        dependencies: [WORKFLOW_STEPS.CREATE_CAMPAIGN, WORKFLOW_STEPS.UPLOAD_EMPLOYEES],
        description: 'Generate employee pairs'
      },
      [WORKFLOW_STEPS.CONFIRM_SEND]: {
        dependencies: [WORKFLOW_STEPS.CREATE_CAMPAIGN, WORKFLOW_STEPS.UPLOAD_EMPLOYEES, WORKFLOW_STEPS.GENERATE_PAIRS],
        description: 'Confirm pairs and send notifications'
      }
    };

    return requirements[step] || { dependencies: [], description: '' };
  },

  // Workflow validation helpers
  validateStepData: {
    [WORKFLOW_STEPS.CREATE_CAMPAIGN]: (data) => {
      const required = ['title', 'start_date', 'end_date'];
      const missing = required.filter(field => !data[field]);
      return {
        valid: missing.length === 0,
        errors: missing.map(field => `The ${field} field is required`),
        warnings: []
      };
    },

    [WORKFLOW_STEPS.UPLOAD_EMPLOYEES]: (data) => {
      const errors = [];
      const warnings = [];

      if (!data.file_name) {
        errors.push('No file uploaded');
      }

      if (!data.employees_count || data.employees_count < 2) {
        errors.push('At least 2 employees are required for pairing');
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings
      };
    },

    [WORKFLOW_STEPS.DEFINE_CRITERIA]: (data) => {
      const warnings = [];

      if (!data.criteria || data.criteria.length === 0) {
        warnings.push('No matching criteria defined - random matching will be used');
      }

      return {
        valid: true, // This step is optional
        errors: [],
        warnings
      };
    },

    [WORKFLOW_STEPS.GENERATE_PAIRS]: (data) => {
      const errors = [];

      if (!data.pairs_count || data.pairs_count === 0) {
        errors.push('No pairs generated');
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings: []
      };
    },

    [WORKFLOW_STEPS.CONFIRM_SEND]: (data) => {
      const errors = [];

      if (!data.confirmed_pairs || data.confirmed_pairs === 0) {
        errors.push('No pairs confirmed');
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings: []
      };
    }
  }
};
