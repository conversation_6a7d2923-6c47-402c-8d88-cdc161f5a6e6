# Simple test settings without Redis dependency
from .settings import *

# Override cache settings to use dummy cache for testing
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# Disable Redis-related apps for testing
INSTALLED_APPS = [app for app in INSTALLED_APPS if 'django_redis' not in app]

# Ensure we're using the test database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'test_postgres',
        'USER': 'postgres',
        'PASSWORD': 'postgres',
        'HOST': 'localhost',
        'PORT': '5432',
        'TEST': {
            'NAME': 'test_postgres',
        }
    }
}

# Enable query logging for performance testing
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django.db.backends': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# Performance testing settings
DEBUG = True
ENABLE_QUERY_MONITORING = True
