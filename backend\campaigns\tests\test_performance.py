# campaigns/tests/test_performance.py
import time
from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from django.db import connection
from django.test.utils import override_settings
from rest_framework.test import APIClient
from rest_framework import status

from campaigns.models import Campaign, CampaignWorkflowState
from users.models import HRManager
from matching.models import CampaignMatchingCriteria
from employees.models import Employee


User = get_user_model()


class WorkflowStatusPerformanceTest(TestCase):
    """Test performance improvements for workflow status endpoints"""
    
    def setUp(self):
        # Create test user and HR manager
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.hr_manager = HRManager.objects.create(
            user=self.user,
            company_name='Test Company'
        )
        
        # Create test campaigns
        self.campaigns = []
        for i in range(5):
            campaign = Campaign.objects.create(
                title=f'Test Campaign {i+1}',
                description=f'Description for campaign {i+1}',
                start_date='2024-01-01',
                end_date='2024-12-31',
                hr_manager=self.hr_manager
            )
            self.campaigns.append(campaign)
            
            # Create workflow state
            CampaignWorkflowState.objects.create(
                campaign=campaign,
                current_step=2,
                completed_steps=[1],
                step_data={'1': {'title': campaign.title}}
            )
            
            # Create some matching criteria
            CampaignMatchingCriteria.objects.create(
                campaign=campaign,
                attribute_key='department',
                rule='not_same'
            )
            
            # Create some employees
            for j in range(3):
                Employee.objects.create(
                    campaign=campaign,
                    name=f'Employee {j+1}',
                    email=f'employee{j+1}@campaign{i+1}.com'
                )
        
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
    
    def test_workflow_status_query_count(self):
        """Test that workflow status endpoint uses minimal database queries"""
        campaign = self.campaigns[0]
        
        # Reset query count
        connection.queries_log.clear()
        
        # Make request
        response = self.client.get(f'/campaigns/{campaign.id}/workflow-status/')
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check query count - should be minimal (ideally 1-2 queries)
        query_count = len(connection.queries)
        self.assertLessEqual(
            query_count, 3, 
            f"Too many queries ({query_count}). Expected <= 3 queries."
        )
        
        # Print queries for debugging
        if query_count > 2:
            print(f"\nQueries executed ({query_count}):")
            for i, query in enumerate(connection.queries, 1):
                print(f"{i}. {query['sql'][:100]}...")
    
    def test_workflow_status_response_time(self):
        """Test that workflow status endpoint responds quickly"""
        campaign = self.campaigns[0]
        
        # Measure response time
        start_time = time.time()
        response = self.client.get(f'/campaigns/{campaign.id}/workflow-status/')
        end_time = time.time()
        
        response_time = end_time - start_time
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Response should be fast (< 100ms for simple case)
        self.assertLess(
            response_time, 0.1,
            f"Response too slow: {response_time:.3f}s. Expected < 0.1s"
        )
    
    @override_settings(DEBUG=False)
    def test_workflow_status_caching(self):
        """Test that caching works correctly"""
        campaign = self.campaigns[0]
        url = f'/campaigns/{campaign.id}/workflow-status/'
        
        # First request - should hit database
        connection.queries_log.clear()
        response1 = self.client.get(url)
        first_query_count = len(connection.queries)
        
        # Second request - should use cache
        connection.queries_log.clear()
        response2 = self.client.get(url)
        second_query_count = len(connection.queries)
        
        # Check responses are identical
        self.assertEqual(response1.status_code, status.HTTP_200_OK)
        self.assertEqual(response2.status_code, status.HTTP_200_OK)
        self.assertEqual(response1.data, response2.data)
        
        # Second request should use fewer queries (cache hit)
        self.assertLess(
            second_query_count, first_query_count,
            f"Cache not working. First: {first_query_count}, Second: {second_query_count}"
        )
    
    def test_bulk_workflow_status_performance(self):
        """Test bulk workflow status endpoint performance"""
        campaign_ids = [c.id for c in self.campaigns]
        
        # Reset query count
        connection.queries_log.clear()
        
        # Make bulk request
        response = self.client.post(
            '/campaigns/bulk-workflow-status/',
            {'campaign_ids': campaign_ids},
            format='json'
        )
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), len(campaign_ids))
        
        # Check query count - should be efficient for bulk operation
        query_count = len(connection.queries)
        # For 5 campaigns, should use much fewer than 5x individual queries
        self.assertLessEqual(
            query_count, 10,
            f"Too many queries for bulk operation ({query_count}). Expected <= 10 queries."
        )
    
    def test_workflow_status_with_missing_state(self):
        """Test performance when workflow state doesn't exist"""
        # Create campaign without workflow state
        campaign = Campaign.objects.create(
            title='No Workflow Campaign',
            description='Campaign without workflow state',
            start_date='2024-01-01',
            end_date='2024-12-31',
            hr_manager=self.hr_manager
        )
        
        # Reset query count
        connection.queries_log.clear()
        
        # Make request
        response = self.client.get(f'/campaigns/{campaign.id}/workflow-status/')
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Should create workflow state efficiently
        query_count = len(connection.queries)
        self.assertLessEqual(
            query_count, 5,
            f"Too many queries for workflow state creation ({query_count}). Expected <= 5 queries."
        )
        
        # Verify workflow state was created
        self.assertTrue(
            CampaignWorkflowState.objects.filter(campaign=campaign).exists()
        )


class QueryOptimizationTest(TestCase):
    """Test specific query optimizations"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.hr_manager = HRManager.objects.create(
            user=self.user,
            company_name='Test Company'
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
    
    def test_select_related_optimization(self):
        """Test that select_related is used properly"""
        campaign = Campaign.objects.create(
            title='Test Campaign',
            description='Test Description',
            start_date='2024-01-01',
            end_date='2024-12-31',
            hr_manager=self.hr_manager
        )
        
        # Reset query count
        connection.queries_log.clear()
        
        # Make request
        response = self.client.get(f'/campaigns/{campaign.id}/workflow-status/')
        
        # Check that we don't have N+1 queries
        queries = connection.queries
        
        # Look for efficient joins in the queries
        has_join_query = any(
            'JOIN' in query['sql'].upper() or 'SELECT_RELATED' in str(query)
            for query in queries
        )
        
        # Should use joins efficiently
        self.assertTrue(
            len(queries) <= 3,
            f"Query optimization not working. Got {len(queries)} queries: {[q['sql'][:50] for q in queries]}"
        )
