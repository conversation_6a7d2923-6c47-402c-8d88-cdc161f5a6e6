# campaigns/tests/test_basic_performance.py
"""
Basic performance tests that don't require Redis caching
"""
from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from django.db import connection
from rest_framework.test import APIClient
from rest_framework import status

from campaigns.models import Campaign, CampaignWorkflowState
from users.models import HRManager
from employees.models import Employee


User = get_user_model()


@override_settings(
    CACHES={
        'default': {
            'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
        }
    }
)
class BasicWorkflowPerformanceTest(TestCase):
    """Test basic performance improvements without Redis dependency"""
    
    def setUp(self):
        # Create test user and HR manager
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.hr_manager = HRManager.objects.create(
            user=self.user,
            company_name='Test Company'
        )
        
        # Create test campaign
        self.campaign = Campaign.objects.create(
            title='Test Campaign',
            description='Test Description',
            start_date='2024-01-01',
            end_date='2024-12-31',
            hr_manager=self.hr_manager
        )
        
        # Create workflow state
        self.workflow_state = CampaignWorkflowState.objects.create(
            campaign=self.campaign,
            current_step=2,
            completed_steps=[1],
            step_data={'1': {'title': self.campaign.title}}
        )
        
        # Create some employees
        for i in range(3):
            Employee.objects.create(
                campaign=self.campaign,
                name=f'Employee {i+1}',
                email=f'employee{i+1}@test.com'
            )
        
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
    
    def test_workflow_status_query_optimization(self):
        """Test that workflow status endpoint uses optimized queries"""
        # Reset query count
        connection.queries_log.clear()
        
        # Make request
        response = self.client.get(f'/campaigns/{self.campaign.id}/workflow-status/')
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('current_step', response.data)
        self.assertIn('completed_steps', response.data)
        
        # Check query count - should be minimal
        query_count = len(connection.queries)
        self.assertLessEqual(
            query_count, 4, 
            f"Too many queries ({query_count}). Expected <= 4 queries. "
            f"Queries: {[q['sql'][:100] for q in connection.queries]}"
        )
        
        # Verify the response contains expected data
        self.assertEqual(response.data['current_step'], 2)
        self.assertEqual(response.data['completed_steps'], [1])
    
    def test_workflow_status_with_missing_state(self):
        """Test performance when workflow state doesn't exist"""
        # Create campaign without workflow state
        campaign = Campaign.objects.create(
            title='No Workflow Campaign',
            description='Campaign without workflow state',
            start_date='2024-01-01',
            end_date='2024-12-31',
            hr_manager=self.hr_manager
        )
        
        # Reset query count
        connection.queries_log.clear()
        
        # Make request
        response = self.client.get(f'/campaigns/{campaign.id}/workflow-status/')
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Should create workflow state efficiently
        query_count = len(connection.queries)
        self.assertLessEqual(
            query_count, 6,
            f"Too many queries for workflow state creation ({query_count}). Expected <= 6 queries."
        )
        
        # Verify workflow state was created
        self.assertTrue(
            CampaignWorkflowState.objects.filter(campaign=campaign).exists()
        )
        
        # Verify default values
        workflow_state = CampaignWorkflowState.objects.get(campaign=campaign)
        self.assertEqual(workflow_state.current_step, 2)
        self.assertEqual(workflow_state.completed_steps, [1])
    
    def test_workflow_status_response_structure(self):
        """Test that the response has the expected structure"""
        response = self.client.get(f'/campaigns/{self.campaign.id}/workflow-status/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check required fields
        required_fields = ['current_step', 'completed_steps', 'step_data', 'updated_at']
        for field in required_fields:
            self.assertIn(field, response.data, f"Missing field: {field}")
    
    def test_workflow_status_unauthorized(self):
        """Test that unauthorized users can't access workflow status"""
        # Create another user
        other_user = User.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Try to access with different user
        self.client.force_authenticate(user=other_user)
        response = self.client.get(f'/campaigns/{self.campaign.id}/workflow-status/')
        
        # Should be forbidden or not found
        self.assertIn(response.status_code, [status.HTTP_403_FORBIDDEN, status.HTTP_404_NOT_FOUND])
    
    def test_workflow_status_nonexistent_campaign(self):
        """Test response for nonexistent campaign"""
        response = self.client.get('/campaigns/99999/workflow-status/')
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_bulk_workflow_status_basic(self):
        """Test basic bulk workflow status functionality"""
        # Create additional campaigns
        campaigns = [self.campaign]
        for i in range(2):
            campaign = Campaign.objects.create(
                title=f'Bulk Test Campaign {i+1}',
                description=f'Description {i+1}',
                start_date='2024-01-01',
                end_date='2024-12-31',
                hr_manager=self.hr_manager
            )
            campaigns.append(campaign)
            
            # Create workflow state
            CampaignWorkflowState.objects.create(
                campaign=campaign,
                current_step=2,
                completed_steps=[1],
                step_data={'1': {'title': campaign.title}}
            )
        
        campaign_ids = [c.id for c in campaigns]
        
        # Reset query count
        connection.queries_log.clear()
        
        # Make bulk request
        response = self.client.post(
            '/campaigns/bulk-workflow-status/',
            {'campaign_ids': campaign_ids},
            format='json'
        )
        
        # Check response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), len(campaign_ids))
        
        # Check query efficiency for bulk operation
        query_count = len(connection.queries)
        # Should be much more efficient than individual requests
        self.assertLessEqual(
            query_count, 8,
            f"Too many queries for bulk operation ({query_count}). Expected <= 8 queries."
        )


class QueryOptimizationTest(TestCase):
    """Test specific query optimizations"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.hr_manager = HRManager.objects.create(
            user=self.user,
            company_name='Test Company'
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
    
    def test_select_related_usage(self):
        """Test that select_related is used properly"""
        campaign = Campaign.objects.create(
            title='Test Campaign',
            description='Test Description',
            start_date='2024-01-01',
            end_date='2024-12-31',
            hr_manager=self.hr_manager
        )
        
        # Reset query count
        connection.queries_log.clear()
        
        # Make request
        response = self.client.get(f'/campaigns/{campaign.id}/workflow-status/')
        
        # Check that we don't have excessive queries
        queries = connection.queries
        query_count = len(queries)
        
        # Should use efficient queries
        self.assertLessEqual(
            query_count, 5,
            f"Query optimization not working. Got {query_count} queries"
        )
        
        # Check for JOIN usage in queries (indicates select_related is working)
        has_efficient_query = any(
            'JOIN' in query['sql'].upper() or len(query['sql']) > 100
            for query in queries
        )
        
        # At least one query should be a complex join
        self.assertTrue(
            has_efficient_query or query_count <= 2,
            "Expected either efficient JOINs or very few queries"
        )
