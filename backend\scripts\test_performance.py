#!/usr/bin/env python
"""
Performance testing script for campaign workflow status endpoints
Run this script to test the performance improvements
"""

import os
import sys
import django
import time
import requests
from django.db import connection

# Setup Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'coffee_meetings_platform.settings')
django.setup()

from django.contrib.auth import get_user_model
from campaigns.models import Campaign, CampaignWorkflowState
from users.models import HRManager
from matching.models import CampaignMatchingCriteria
from employees.models import Employee

User = get_user_model()


def create_test_data():
    """Create test data for performance testing"""
    print("Creating test data...")
    
    # Create test user
    user, created = User.objects.get_or_create(
        username='perftest',
        defaults={
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
    )
    
    hr_manager, created = HRManager.objects.get_or_create(
        user=user,
        defaults={'company_name': 'Performance Test Company'}
    )
    
    # Create test campaigns
    campaigns = []
    for i in range(10):
        campaign, created = Campaign.objects.get_or_create(
            title=f'Performance Test Campaign {i+1}',
            hr_manager=hr_manager,
            defaults={
                'description': f'Description for performance test campaign {i+1}',
                'start_date': '2024-01-01',
                'end_date': '2024-12-31',
            }
        )
        campaigns.append(campaign)
        
        # Create workflow state
        workflow_state, created = CampaignWorkflowState.objects.get_or_create(
            campaign=campaign,
            defaults={
                'current_step': 2,
                'completed_steps': [1],
                'step_data': {'1': {'title': campaign.title}}
            }
        )
        
        # Create matching criteria
        CampaignMatchingCriteria.objects.get_or_create(
            campaign=campaign,
            attribute_key='department',
            defaults={'rule': 'not_same'}
        )
        
        # Create employees
        for j in range(5):
            Employee.objects.get_or_create(
                campaign=campaign,
                name=f'Employee {j+1}',
                defaults={'email': f'employee{j+1}@campaign{i+1}.com'}
            )
    
    print(f"Created {len(campaigns)} campaigns with workflow states")
    return campaigns, user


def test_individual_requests(campaigns, user):
    """Test individual workflow status requests"""
    print("\nTesting individual workflow status requests...")
    
    from django.test import Client
    client = Client()
    client.force_login(user)
    
    total_time = 0
    total_queries = 0
    
    for campaign in campaigns[:5]:  # Test first 5 campaigns
        # Reset query count
        connection.queries_log.clear()
        
        # Time the request
        start_time = time.time()
        response = client.get(f'/campaigns/{campaign.id}/workflow-status/')
        end_time = time.time()
        
        request_time = end_time - start_time
        query_count = len(connection.queries)
        
        total_time += request_time
        total_queries += query_count
        
        print(f"Campaign {campaign.id}: {request_time:.3f}s, {query_count} queries")
        
        # Show slow queries
        if query_count > 3:
            print("  Queries:")
            for i, query in enumerate(connection.queries, 1):
                query_time = float(query.get('time', 0))
                print(f"    {i}. {query_time:.3f}s - {query['sql'][:80]}...")
    
    avg_time = total_time / 5
    avg_queries = total_queries / 5
    
    print(f"\nAverage per request: {avg_time:.3f}s, {avg_queries:.1f} queries")
    return avg_time, avg_queries


def test_bulk_request(campaigns, user):
    """Test bulk workflow status request"""
    print("\nTesting bulk workflow status request...")
    
    from django.test import Client
    client = Client()
    client.force_login(user)
    
    campaign_ids = [c.id for c in campaigns[:5]]
    
    # Reset query count
    connection.queries_log.clear()
    
    # Time the request
    start_time = time.time()
    response = client.post(
        '/campaigns/bulk-workflow-status/',
        {'campaign_ids': campaign_ids},
        content_type='application/json'
    )
    end_time = time.time()
    
    request_time = end_time - start_time
    query_count = len(connection.queries)
    
    print(f"Bulk request: {request_time:.3f}s, {query_count} queries for {len(campaign_ids)} campaigns")
    
    if query_count > 0:
        print("Queries:")
        for i, query in enumerate(connection.queries, 1):
            query_time = float(query.get('time', 0))
            print(f"  {i}. {query_time:.3f}s - {query['sql'][:80]}...")
    
    return request_time, query_count


def test_cache_performance(campaigns, user):
    """Test caching performance"""
    print("\nTesting cache performance...")
    
    from django.test import Client
    from django.core.cache import cache
    
    client = Client()
    client.force_login(user)
    
    campaign = campaigns[0]
    url = f'/campaigns/{campaign.id}/workflow-status/'
    
    # Clear cache
    cache.clear()
    
    # First request (cache miss)
    connection.queries_log.clear()
    start_time = time.time()
    response1 = client.get(url)
    end_time = time.time()
    
    first_time = end_time - start_time
    first_queries = len(connection.queries)
    
    # Second request (cache hit)
    connection.queries_log.clear()
    start_time = time.time()
    response2 = client.get(url)
    end_time = time.time()
    
    second_time = end_time - start_time
    second_queries = len(connection.queries)
    
    print(f"First request (cache miss): {first_time:.3f}s, {first_queries} queries")
    print(f"Second request (cache hit): {second_time:.3f}s, {second_queries} queries")
    print(f"Cache speedup: {first_time/second_time:.1f}x faster")
    
    return first_time, second_time, first_queries, second_queries


def main():
    """Main performance testing function"""
    print("Campaign Workflow Status Performance Test")
    print("=" * 50)
    
    # Create test data
    campaigns, user = create_test_data()
    
    # Test individual requests
    avg_time, avg_queries = test_individual_requests(campaigns, user)
    
    # Test bulk request
    bulk_time, bulk_queries = test_bulk_request(campaigns, user)
    
    # Test cache performance
    cache_results = test_cache_performance(campaigns, user)
    
    # Summary
    print("\n" + "=" * 50)
    print("PERFORMANCE SUMMARY")
    print("=" * 50)
    print(f"Individual requests: {avg_time:.3f}s avg, {avg_queries:.1f} queries avg")
    print(f"Bulk request: {bulk_time:.3f}s for 5 campaigns, {bulk_queries} queries")
    print(f"Cache performance: {cache_results[0]:.3f}s -> {cache_results[1]:.3f}s")
    
    # Performance targets
    print("\nPerformance Targets:")
    print(f"✓ Individual request < 0.1s: {'PASS' if avg_time < 0.1 else 'FAIL'}")
    print(f"✓ Individual request < 3 queries: {'PASS' if avg_queries < 3 else 'FAIL'}")
    print(f"✓ Bulk request < 0.5s: {'PASS' if bulk_time < 0.5 else 'FAIL'}")
    print(f"✓ Cache speedup > 2x: {'PASS' if cache_results[0]/cache_results[1] > 2 else 'FAIL'}")


if __name__ == '__main__':
    main()
