# campaigns/middleware.py
import time
import logging
from django.db import connection
from django.conf import settings

logger = logging.getLogger(__name__)

class QueryPerformanceMiddleware:
    """
    Middleware to monitor database query performance for campaign-related endpoints
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Skip monitoring in production unless explicitly enabled
        if not settings.DEBUG and not getattr(settings, 'ENABLE_QUERY_MONITORING', False):
            return self.get_response(request)
        
        # Only monitor campaign-related endpoints
        if not self._should_monitor(request.path):
            return self.get_response(request)
        
        # Reset query count
        initial_queries = len(connection.queries)
        start_time = time.time()
        
        # Process the request
        response = self.get_response(request)
        
        # Calculate performance metrics
        end_time = time.time()
        total_time = end_time - start_time
        query_count = len(connection.queries) - initial_queries
        
        # Log performance metrics
        self._log_performance(request, response, total_time, query_count)
        
        # Add performance headers for debugging
        if settings.DEBUG:
            response['X-DB-Queries'] = str(query_count)
            response['X-Response-Time'] = f"{total_time:.3f}s"
        
        return response
    
    def _should_monitor(self, path):
        """Check if the path should be monitored"""
        monitored_patterns = [
            '/campaigns/',
            '/matching/',
            '/employees/',
        ]
        return any(pattern in path for pattern in monitored_patterns)
    
    def _log_performance(self, request, response, total_time, query_count):
        """Log performance metrics"""
        # Log slow requests (>500ms or >5 queries)
        is_slow = total_time > 0.5 or query_count > 5
        
        log_data = {
            'method': request.method,
            'path': request.path,
            'status_code': response.status_code,
            'response_time': f"{total_time:.3f}s",
            'query_count': query_count,
            'user_id': getattr(request.user, 'id', None) if hasattr(request, 'user') else None,
        }
        
        if is_slow:
            logger.warning(
                f"Slow request detected: {request.method} {request.path} "
                f"took {total_time:.3f}s with {query_count} queries",
                extra=log_data
            )
            
            # Log individual queries for slow requests in debug mode
            if settings.DEBUG and query_count > 0:
                recent_queries = connection.queries[-query_count:]
                for i, query in enumerate(recent_queries, 1):
                    query_time = float(query.get('time', 0))
                    if query_time > 0.1:  # Log queries taking >100ms
                        logger.warning(
                            f"Slow query #{i}: {query_time:.3f}s - {query['sql'][:200]}..."
                        )
        else:
            logger.info(
                f"Request: {request.method} {request.path} "
                f"({total_time:.3f}s, {query_count} queries)",
                extra=log_data
            )


class CampaignQueryOptimizationMiddleware:
    """
    Middleware specifically for optimizing campaign workflow queries
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Check if this is a workflow status request
        if 'workflow-status' in request.path:
            # Add query optimization hints to the request
            request._query_optimization = {
                'use_select_related': True,
                'use_prefetch_related': True,
                'cache_enabled': not settings.DEBUG
            }
        
        return self.get_response(request)


class DatabaseQueryLogger:
    """
    Utility class for logging database queries in specific contexts
    """
    
    @staticmethod
    def log_queries_for_view(view_name, func):
        """Decorator to log queries for a specific view"""
        def wrapper(*args, **kwargs):
            if not settings.DEBUG:
                return func(*args, **kwargs)
            
            initial_queries = len(connection.queries)
            start_time = time.time()
            
            result = func(*args, **kwargs)
            
            end_time = time.time()
            total_time = end_time - start_time
            query_count = len(connection.queries) - initial_queries
            
            logger.info(
                f"View {view_name}: {total_time:.3f}s, {query_count} queries"
            )
            
            # Log individual queries if there are many
            if query_count > 3:
                recent_queries = connection.queries[-query_count:]
                for i, query in enumerate(recent_queries, 1):
                    query_time = float(query.get('time', 0))
                    logger.debug(
                        f"Query #{i}: {query_time:.3f}s - {query['sql'][:100]}..."
                    )
            
            return result
        return wrapper
