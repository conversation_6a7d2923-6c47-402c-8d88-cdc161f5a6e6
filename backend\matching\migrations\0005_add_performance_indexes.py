# Generated migration for matching performance optimization

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('matching', '0004_add_missing_fields'),
    ]

    operations = [
        # Add performance index for matching criteria queries
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS matching_criteria_campaign_created_idx ON matching_campaignmatchingcriteria (campaign_id, created_at);",
            reverse_sql="DROP INDEX IF EXISTS matching_criteria_campaign_created_idx;"
        ),
        # Add index for campaign lookup
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS matching_criteria_campaign_idx ON matching_campaignmatchingcriteria (campaign_id);",
            reverse_sql="DROP INDEX IF EXISTS matching_criteria_campaign_idx;"
        ),
    ]
